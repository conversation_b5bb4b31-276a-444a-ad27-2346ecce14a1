import AppKit
import CoreGraphics

/// Enhanced screen detection service using proven Workspace patterns
/// Adopts the same coordinate system and display detection logic as the Workspace feature
class ScreenDetectionService: ScreenDetectable {
    private let logger = LoggingService.shared
    private let serviceName = "ScreenDetectionService"

    // Enhanced coordinate system using Workspace patterns
    private var coordinateSystem: CoordinateSystem?
    private var lastDisplayRefresh: Date = Date.distantPast
    private let displayRefreshInterval: TimeInterval = 1.0  // Refresh display info every second if needed

    init() {
        setupDisplayChangeNotifications()
        refreshDisplayInformation()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - Display Change Notifications

    private func setupDisplayChangeNotifications() {
        NotificationCenter.default.addObserver(
            forName: NSApplication.didChangeScreenParametersNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.logger.info(
                "Screen parameters changed - refreshing display information",
                service: self?.serviceName ?? "ScreenDetectionService")
            self?.refreshDisplayInformation()
        }
    }

    // MARK: - Enhanced Display Information Management

    private func refreshDisplayInformation() {
        let now = Date()
        if now.timeIntervalSince(lastDisplayRefresh) < displayRefreshInterval {
            return  // Don't refresh too frequently
        }

        lastDisplayRefresh = now

        // Get all displays using the proven Workspace approach
        let displays = WindowLayoutManager.getAllDisplays()
        coordinateSystem = CoordinateSystem(displays: displays)

        logger.info(
            "┌─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )
        logger.info(
            "│ ENHANCED SCREEN DETECTION SYSTEM",
            service: serviceName
        )
        logger.info(
            "├─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )
        logger.info(
            "│ - Total displays: \(displays.count)",
            service: serviceName
        )
        if let virtualBounds = coordinateSystem?.virtualBounds {
            logger.info(
                "│ - Virtual bounds: \(virtualBounds)",
                service: serviceName
            )
        }
        logger.info(
            "└─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )

        // Log detailed display information
        for (index, display) in displays.enumerated() {
            logger.debug(
                "Display \(index): ID=\(display.id), Frame=\(display.logicalFrame), Main=\(display.isMain)",
                service: serviceName,
                category: .screenDetection
            )
        }
    }

    func getScreenContaining(_ rect: CGRect) -> NSScreen? {
        // Ensure we have fresh display information
        refreshDisplayInformation()

        guard coordinateSystem != nil else {
            logger.warning(
                "No coordinate system available, falling back to main screen", service: serviceName)
            return NSScreen.main
        }

        logger.debug(
            "Finding screen containing rect: \(rect)", service: serviceName,
            category: .screenDetection)

        // Use the proven Workspace approach for display detection
        if let bestDisplay = WindowLayoutManager.findBestDisplay(for: rect) {
            // Convert DisplayInfo back to NSScreen
            if let nsScreen = NSScreen.screens.first(where: { screen in
                if let screenNumber = screen.deviceDescription[
                    NSDeviceDescriptionKey("NSScreenNumber")] as? NSNumber
                {
                    return CGDirectDisplayID(screenNumber.uint32Value) == bestDisplay.id
                }
                return false
            }) {
                logger.debug(
                    "Selected screen using Workspace logic: \(bestDisplay.id)",
                    service: serviceName,
                    category: .screenDetection
                )
                return nsScreen
            }
        }

        // Fallback to main screen
        logger.warning(
            "Could not find best display, falling back to main screen", service: serviceName)
        return NSScreen.main
    }

    /// Get all available screens using enhanced display information
    func getAllScreens() -> [NSScreen] {
        // Ensure we have fresh display information
        refreshDisplayInformation()

        let screens = NSScreen.screens

        logger.debug(
            "Getting all screens: \(screens.count) available", service: serviceName,
            category: .screenDetection)

        // Use the enhanced coordinate system for consistent ordering
        if let coordSystem = coordinateSystem {
            // Sort screens to match the display ordering from WindowLayoutManager
            let sortedScreens = screens.sorted { screen1, screen2 in
                // Get display IDs
                guard
                    let id1 = screen1.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
                        as? NSNumber,
                    let id2 = screen2.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
                        as? NSNumber
                else {
                    return false
                }

                let displayID1 = CGDirectDisplayID(id1.uint32Value)
                let displayID2 = CGDirectDisplayID(id2.uint32Value)

                // Find corresponding DisplayInfo objects
                let display1 = coordSystem.displays.first { $0.id == displayID1 }
                let display2 = coordSystem.displays.first { $0.id == displayID2 }

                // Main display comes first
                if display1?.isMain == true { return true }
                if display2?.isMain == true { return false }

                // Then sort by position (left to right, top to bottom)
                if let d1 = display1, let d2 = display2 {
                    if abs(d1.logicalFrame.origin.y - d2.logicalFrame.origin.y) < 10 {
                        return d1.logicalFrame.origin.x < d2.logicalFrame.origin.x
                    }
                    return d1.logicalFrame.origin.y < d2.logicalFrame.origin.y
                }

                return false
            }

            logger.debug("Screens sorted using enhanced coordinate system", service: serviceName)
            return sortedScreens
        }

        // Fallback to original ordering
        return screens
    }

    // MARK: - Enhanced Display Detection

    /// Get the enhanced coordinate system for advanced multi-display operations
    func getCoordinateSystem() -> CoordinateSystem? {
        refreshDisplayInformation()
        return coordinateSystem
    }

    /// Get display information for a specific screen
    func getDisplayInfo(for screen: NSScreen) -> DisplayInfo? {
        guard let coordSystem = coordinateSystem,
            let screenNumber = screen.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
                as? NSNumber
        else {
            return nil
        }

        let displayID = CGDirectDisplayID(screenNumber.uint32Value)
        return coordSystem.displays.first { $0.id == displayID }
    }
}

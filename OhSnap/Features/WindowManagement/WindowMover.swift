import AppKit
import Carbon

/// Enhanced window mover using proven Workspace patterns
/// Adopts the same coordinate system and display handling logic as the Workspace feature
class WindowMover {
    private let accessibilityElement: AccessibilityElement
    private let calculationService: WindowCalculationService
    private let screenDetection: ScreenDetectionService
    private let logger = LoggingService.shared
    private let serviceName = "WindowMover"

    init(
        accessibilityElement: AccessibilityElement = AccessibilityElement(),
        calculationService: WindowCalculationService = WindowCalculationService(),
        screenDetection: ScreenDetectionService = ScreenDetectionService()
    ) {
        self.accessibilityElement = accessibilityElement
        self.calculationService = calculationService
        self.screenDetection = screenDetection

        logger.info("Initialized with enhanced coordinate system support", service: serviceName)
    }

    func moveWindow(
        _ window: AXUIElement,
        to direction: WindowDirection,
        on screen: NSScreen,
        frameAdjustment: CGRect? = nil
    ) async throws {
        // Clear any existing debug overlays to prevent performance issues
        ScreenshotDebugger.shared.clearAllDebugOverlays()

        // Get current window info
        guard let windowInfo = try? await accessibilityElement.windowInfo(for: window) else {
            throw WindowMoveError.failedToGetWindowInfo
        }

        // Get enhanced display information using Workspace patterns
        guard let displayInfo = screenDetection.getDisplayInfo(for: screen) else {
            logger.warning(
                "Could not get display info for screen, proceeding with fallback",
                service: serviceName)
            throw WindowMoveError.failedToGetDisplayInfo
        }

        // Log comprehensive window move information using Workspace patterns
        logger.info(
            "┌─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )
        logger.info(
            "│ ENHANCED WINDOW MOVE OPERATION",
            service: serviceName
        )
        logger.info(
            "├─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )
        logger.info(
            "│ - Direction: \(direction)",
            service: serviceName
        )
        logger.info(
            "│ - Display ID: \(displayInfo.id)",
            service: serviceName
        )
        logger.info(
            "│ - Is main display: \(displayInfo.isMain)",
            service: serviceName
        )
        logger.info(
            "│ - Current window frame: \(windowInfo.frame)",
            service: serviceName
        )
        logger.info(
            "│ - Display logical frame: \(displayInfo.logicalFrame)",
            service: serviceName
        )
        logger.info(
            "│ - Display visible frame: \(displayInfo.visibleFrame)",
            service: serviceName
        )
        logger.info(
            "│ - Scale factor: \(displayInfo.scaleFactor)",
            service: serviceName
        )
        logger.info(
            "│ - Height difference from main: \(displayInfo.heightDifferenceFromMain)",
            service: serviceName
        )
        logger.info(
            "└─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )

        // Calculate target frame using enhanced coordinate system
        var targetFrame = calculationService.calculateWindowRect(
            for: direction,
            window: windowInfo,
            screen: screen
        )

        // Check if this is a bottom-aligned window
        let isBottomAligned =
            direction == .bottomHalf || direction == .bottomLeft || direction == .bottomRight
            || direction == .bottomLeftQuarter || direction == .bottomRightQuarter

        // Apply any frame adjustments
        if let adjustment = frameAdjustment {
            targetFrame = targetFrame.offsetBy(
                dx: adjustment.origin.x,
                dy: adjustment.origin.y
            )
            targetFrame.size.width += adjustment.size.width
            targetFrame.size.height += adjustment.size.height

            logger.debug("Applied frame adjustment: \(adjustment)", service: serviceName)
        }

        // Ensure the frame is properly adjusted using enhanced coordinate system
        let boundaryAdjustedFrame = DisplayScaleManager.shared.adjustRectForScreen(
            rect: targetFrame, screen: screen)
        targetFrame = boundaryAdjustedFrame

        // Draw a debug overlay to visualize the target rect
        ScreenshotDebugger.shared.drawDebugOverlay(rect: targetFrame)

        logger.info("Final target frame: \(targetFrame)", service: serviceName)

        // Move window with enhanced retry mechanism
        try await moveWindowWithRetry(
            window, to: targetFrame, isBottomAligned: isBottomAligned, direction: direction)
    }

    private func moveWindowWithRetry(
        _ window: AXUIElement,
        to frame: CGRect,
        retryCount: Int = 3,
        isBottomAligned: Bool = false,
        direction: WindowDirection? = nil
    ) async throws {
        var lastError: Error?

        // Check if we can get window info (validates the window is accessible)
        guard (try? await accessibilityElement.windowInfo(for: window)) != nil else {
            throw WindowMoveError.failedToGetWindowInfo
        }

        // Get the screen containing the frame
        let screen = screenDetection.getScreenContaining(frame) ?? NSScreen.main!

        // Use a unified approach for all displays
        for attempt in 1...retryCount {
            do {
                // Get the current screen's visible frame before setting the frame
                let visibleFrameBefore = screen.visibleFrame

                // Flip the coordinates using Rectangle's approach
                // This ensures a 1-to-1 match with Rectangle's window positioning
                let flippedFrame = frame.screenFlipped
                logger.debug(
                    "Original frame: \(frame), Flipped frame: \(flippedFrame)",
                    service: serviceName)
                try await accessibilityElement.setFrame(
                    window, flippedFrame, isBottomAligned: isBottomAligned)

                // Verify the window moved to the correct position
                let currentFrame = try await accessibilityElement.getFrame(window)

                // Check if the dock has appeared or disappeared during this operation
                let visibleFrameAfter = screen.visibleFrame
                let dockHeightChanged =
                    abs(visibleFrameBefore.height - visibleFrameAfter.height) > 10
                let dockPositionChanged = abs(visibleFrameBefore.minY - visibleFrameAfter.minY) > 10

                // Also check if the window is not at the correct Y position
                let windowNotAtCorrectY =
                    isBottomAligned && abs(currentFrame.minY - visibleFrameAfter.minY) > 10

                if dockHeightChanged || dockPositionChanged || windowNotAtCorrectY {
                    logger.debug(
                        "Detected dock appearance/disappearance during window positioning",
                        service: serviceName)
                    logger.debug(
                        "Visible frame before: \(visibleFrameBefore), after: \(visibleFrameAfter)",
                        service: serviceName)

                    // If the dock appeared/disappeared and this is a bottom-aligned window,
                    // we need to recalculate the target frame and try again
                    if isBottomAligned {
                        logger.debug(
                            "Recalculating frame for bottom-aligned window after dock change",
                            service: serviceName)

                        // Get the window info again
                        if let windowInfo = try? await accessibilityElement.windowInfo(for: window)
                        {
                            // Recalculate the target frame with the new visible frame
                            // Use the provided direction if available, otherwise fall back to bottomHalf
                            let recalculationDirection = direction ?? .bottomHalf

                            logger.debug(
                                "Recalculating with direction: \(recalculationDirection)",
                                service: serviceName,
                                category: .windowPositioning
                            )

                            let newTargetFrame = calculationService.calculateWindowRect(
                                for: recalculationDirection,
                                window: windowInfo,
                                screen: screen
                            )

                            logger.debug(
                                "Original target frame: \(frame), new target frame: \(newTargetFrame)",
                                service: serviceName)

                            // Try to position with the new frame
                            // Flip the coordinates using Rectangle's approach
                            // This ensures a 1-to-1 match with Rectangle's window positioning
                            let flippedFrame = newTargetFrame.screenFlipped
                            logger.debug(
                                "Original frame: \(newTargetFrame), Flipped frame: \(flippedFrame)",
                                service: serviceName)
                            try await accessibilityElement.setFrame(
                                window, flippedFrame, isBottomAligned: true)

                            // Get the final position
                            let finalFrame = try await accessibilityElement.getFrame(window)

                            // Check if the window is still not at the correct Y position
                            // This happens when the window is partially overlapping with the dock
                            if abs(finalFrame.minY - visibleFrameAfter.minY) > 10 {
                                logger.debug(
                                    "Window still not at correct Y position. Current Y: \(finalFrame.minY), Should be: \(visibleFrameAfter.minY)",
                                    service: serviceName)

                                // Create a corrected frame with the proper Y position
                                let correctedFrame = CGRect(
                                    x: finalFrame.minX,
                                    y: visibleFrameAfter.minY,  // Use the visible frame's minY
                                    width: finalFrame.width,
                                    height: finalFrame.height
                                )

                                logger.debug(
                                    "Applying final Y position correction: \(correctedFrame)",
                                    service: serviceName)

                                // Apply the correction
                                // Flip the coordinates using Rectangle's approach
                                // This ensures a 1-to-1 match with Rectangle's window positioning
                                let flippedFrame = correctedFrame.screenFlipped
                                logger.debug(
                                    "Original frame: \(correctedFrame), Flipped frame: \(flippedFrame)",
                                    service: serviceName)
                                try await accessibilityElement.setFrame(
                                    window, flippedFrame, isBottomAligned: true)

                                // Get the final position after correction
                                let correctedFinalFrame = try await accessibilityElement.getFrame(
                                    window)
                                logger.debug(
                                    "Final frame after Y position correction: \(correctedFinalFrame)",
                                    service: serviceName)

                                // Draw a debug overlay
                                ScreenshotDebugger.shared.drawDebugOverlay(
                                    rect: correctedFinalFrame, color: .blue)
                            } else {
                                logger.debug(
                                    "Final frame after dock change adjustment: \(finalFrame)",
                                    service: serviceName)

                                // Draw a debug overlay
                                ScreenshotDebugger.shared.drawDebugOverlay(
                                    rect: finalFrame, color: .blue)
                            }
                            return
                        }
                    }
                }

                // Use a larger tolerance for later attempts and secondary displays
                let isSecondary = screen != NSScreen.main
                let tolerance = (attempt > 1 ? 2.0 : 1.0) * (isSecondary ? 2.0 : 1.0)

                if framesAreEqual(
                    currentFrame, frame, tolerance: tolerance, isBottomAligned: isBottomAligned)
                {
                    logger.debug(
                        "Window positioned successfully on attempt \(attempt)", service: serviceName
                    )

                    // Draw a debug overlay to visualize the actual rect (in a different color)
                    let color: NSColor = isSecondary ? .blue : .green
                    ScreenshotDebugger.shared.drawDebugOverlay(rect: currentFrame, color: color)
                    return
                }

                logger.debug(
                    "Window position verification failed on attempt \(attempt), retrying...",
                    service: serviceName)

                // Increase delay with each attempt
                try await Task.sleep(nanoseconds: UInt64(50_000_000 * attempt))  // Increasing delay with each attempt
            } catch {
                lastError = error
                logger.error("Error setting window frame: \(error)", service: serviceName)
                if attempt == retryCount { throw error }
            }
        }

        throw lastError ?? WindowMoveError.verificationFailed
    }

    private func framesAreEqual(
        _ frame1: CGRect, _ frame2: CGRect, tolerance: CGFloat, isBottomAligned: Bool = false
    ) -> Bool {
        // For bottom-aligned windows, we need to be more lenient with Y position
        // but not too lenient to mask real positioning issues
        let bottomTolerance = isBottomAligned ? 150.0 : tolerance

        // Log the comparison for debugging
        logger.debug("Comparing frames with tolerance \(tolerance):", service: serviceName)
        logger.debug("  Frame1: \(frame1)", service: serviceName)
        logger.debug("  Frame2: \(frame2)", service: serviceName)
        logger.debug(
            "  Is bottom aligned (from parameter): \(isBottomAligned), Y tolerance: \(bottomTolerance)",
            service: serviceName)

        // Calculate differences
        let xDiff = abs(frame1.origin.x - frame2.origin.x)
        let yDiff = abs(frame1.origin.y - frame2.origin.y)
        let widthDiff = abs(frame1.size.width - frame2.size.width)
        let heightDiff = abs(frame1.size.height - frame2.size.height)

        logger.debug(
            "  Differences - X: \(xDiff), Y: \(yDiff), Width: \(widthDiff), Height: \(heightDiff)",
            service: serviceName)

        // Special handling for bottom-aligned windows when the dock might have appeared
        let positionOK: Bool
        if isBottomAligned {
            // Check if this might be a dock appearance/disappearance scenario
            // In this case, we care more about the window being at the bottom of the screen
            // than the exact Y coordinate
            let screen = NSScreen.screens.first { $0.frame.contains(frame1.center) }
            if let screen = screen {
                // Get the screen's visible frame (accounts for dock)
                let visibleFrame = screen.visibleFrame

                // Calculate how far the window is from the bottom of the visible frame
                let frame1BottomToScreenBottom = abs(frame1.minY - visibleFrame.minY)
                let frame2BottomToScreenBottom = abs(frame2.minY - visibleFrame.minY)

                logger.debug(
                    "  Bottom alignment check - Frame1 distance from bottom: \(frame1BottomToScreenBottom), Frame2 distance from bottom: \(frame2BottomToScreenBottom)",
                    service: serviceName)

                // For bottom-aligned windows, we want to be stricter about the Y position
                // The window should be exactly at the bottom of the visible frame (accounting for tolerance)
                let bottomPositionOK = frame1BottomToScreenBottom <= tolerance

                // X position should still be within tolerance
                positionOK =
                    xDiff <= tolerance && (yDiff <= bottomTolerance || bottomPositionOK)
            } else {
                // Fallback to standard check with increased tolerance
                positionOK = xDiff <= tolerance && yDiff <= bottomTolerance
            }
        } else {
            // Standard position check for non-bottom-aligned windows
            positionOK = xDiff <= tolerance && yDiff <= tolerance
        }

        // For size, we're more lenient - some windows have minimum size constraints
        // We'll consider it OK if the window is at least as big as we wanted
        let sizeOK =
            (frame1.size.width >= frame2.size.width * 0.9 || widthDiff <= tolerance * 5)
            && (frame1.size.height >= frame2.size.height * 0.9 || heightDiff <= tolerance * 5)

        // The result is OK if both position and size are acceptable
        let result = positionOK && sizeOK

        logger.debug("  Position OK: \(positionOK), Size OK: \(sizeOK)", service: serviceName)
        logger.debug("  Result: \(result)", service: serviceName)
        return result
    }

    // MARK: - Enhanced Display Arrangement Detection

    /// Determine if screens are arranged vertically using enhanced coordinate system
    private func isVerticalScreenArrangement(screens: [NSScreen]) -> Bool {
        guard let coordinateSystem = screenDetection.getCoordinateSystem() else {
            logger.warning(
                "No coordinate system available for arrangement detection", service: serviceName)
            return false
        }

        let arrangement = coordinateSystem.getDisplayArrangement()
        let isVertical = arrangement == .vertical || arrangement == .mixed

        logger.debug(
            "Display arrangement detected: \(arrangement), isVertical: \(isVertical)",
            service: serviceName
        )

        return isVertical
    }
}

import AppKit
import CoreGraphics
import Foundation

/// Enhanced window calculation service using proven Workspace patterns
/// Adopts the same coordinate system and display handling logic as the Workspace feature
class WindowCalculationService {
    private let screenDetection: ScreenDetectionService
    private let logger = LoggingService.shared
    private let serviceName = "WindowCalculationService"

    // Calculation classes
    private let standardPositionCalculation = StandardPositionCalculation()
    private let almostMaximizeCalculation = AlmostMaximizeCalculation()
    private let changeSizeCalculation = ChangeSizeCalculation()
    private let specifiedCalculation = SpecifiedCalculation()
    private let leftRightHalfCalculation = LeftRightHalfCalculation()
    private let topBottomHalfCalculation = TopBottomHalfCalculation()

    init(screenDetection: ScreenDetectionService = ScreenDetectionService()) {
        self.screenDetection = screenDetection
        logger.info("Initialized with enhanced coordinate system support", service: serviceName)
    }

    func calculateWindowRect(
        for direction: WindowDirection,
        window: WindowInfo,
        screen: NSScreen,
        visibleFrameOnly: Bool = true
    ) -> CGRect {
        // Get enhanced display information using Workspace patterns
        guard let displayInfo = screenDetection.getDisplayInfo(for: screen) else {
            logger.warning(
                "Could not get display info for screen, using fallback calculation",
                service: serviceName)
            return calculateFallbackRect(
                for: direction, window: window, screen: screen, visibleFrameOnly: visibleFrameOnly)
        }

        // Get coordinate system for comprehensive logging
        let coordinateSystem = screenDetection.getCoordinateSystem()

        // Log comprehensive calculation information using Workspace patterns
        logger.info(
            "┌─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )
        logger.info(
            "│ ENHANCED WINDOW CALCULATION",
            service: serviceName
        )
        logger.info(
            "├─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )
        logger.info(
            "│ - Direction: \(direction)",
            service: serviceName
        )
        logger.info(
            "│ - Display ID: \(displayInfo.id)",
            service: serviceName
        )
        logger.info(
            "│ - Is main display: \(displayInfo.isMain)",
            service: serviceName
        )
        logger.info(
            "│ - Logical frame: \(displayInfo.logicalFrame)",
            service: serviceName
        )
        logger.info(
            "│ - Visible frame: \(displayInfo.visibleFrame)",
            service: serviceName
        )
        logger.info(
            "│ - Scale factor: \(displayInfo.scaleFactor)",
            service: serviceName
        )
        logger.info(
            "│ - Height difference from main: \(displayInfo.heightDifferenceFromMain)",
            service: serviceName
        )
        if let virtualBounds = coordinateSystem?.virtualBounds {
            logger.info(
                "│ - Virtual bounds: \(virtualBounds)",
                service: serviceName
            )
        }
        logger.info(
            "└─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )

        // Create calculation parameters using enhanced display information
        let screenFrame = visibleFrameOnly ? displayInfo.visibleFrame : displayInfo.logicalFrame
        let allScreens = screenDetection.getAllScreens()

        // Create window calculation parameters
        let windowParams = WindowCalculationParameters(
            window: window,
            screens: allScreens,
            currentScreen: screen,
            action: direction
        )

        // Create rect calculation parameters for simple calculations
        let rectParams = RectCalculationParameters(
            window: window,
            visibleFrameOfScreen: screenFrame,
            action: direction,
            frameOfScreen: displayInfo.logicalFrame
        )

        // Perform calculation using enhanced coordinate system
        var result: CGRect

        // Use a unified approach for all displays with enhanced coordinate handling
        switch direction {
        case .leftHalf, .rightHalf:
            // Use the left/right half calculation with multi-display support
            if let calcResult = leftRightHalfCalculation.calculate(windowParams) {
                result = calcResult.rect
            } else {
                // Fallback to standard calculation
                result = standardPositionCalculation.calculateRect(rectParams).rect
            }

        case .topHalf, .bottomHalf:
            // Use the top/bottom half calculation with multi-display support
            if let calcResult = topBottomHalfCalculation.calculate(windowParams) {
                result = calcResult.rect
            } else {
                // Fallback to standard calculation
                result = standardPositionCalculation.calculateRect(rectParams).rect
            }

        case .almostMaximize:
            result = almostMaximizeCalculation.calculateRect(rectParams).rect

        case .larger, .smaller, .largerWidth, .smallerWidth:
            result = changeSizeCalculation.calculateRect(rectParams).rect

        case .specified:
            result = specifiedCalculation.calculateRect(rectParams).rect

        default:
            // Use standard position calculation for all other directions
            result = standardPositionCalculation.calculateRect(rectParams).rect
        }

        // Apply enhanced coordinate transformations using Workspace patterns
        result = applyEnhancedCoordinateTransformations(
            rect: result,
            displayInfo: displayInfo,
            screen: screen,
            coordinateSystem: coordinateSystem
        )

        logger.info("Final calculated rect: \(result)", service: serviceName)
        return result
    }

    // MARK: - Enhanced Coordinate Transformations

    /// Apply enhanced coordinate transformations using proven Workspace patterns
    private func applyEnhancedCoordinateTransformations(
        rect: CGRect,
        displayInfo: DisplayInfo,
        screen: NSScreen,
        coordinateSystem: CoordinateSystem?
    ) -> CGRect {
        var transformedRect = rect

        // Apply height difference compensation if needed (Workspace pattern)
        if !displayInfo.isMain && displayInfo.heightDifferenceFromMain != 0 {
            let compensatedY = WindowLayoutManager.applyHeightDifferenceOffset(
                y: transformedRect.origin.y,
                windowHeight: transformedRect.height,
                display: displayInfo
            )
            transformedRect.origin.y = compensatedY

            logger.debug(
                "Applied height difference compensation: \(displayInfo.heightDifferenceFromMain)",
                service: serviceName
            )
        }

        // Ensure the rect fits within the display's logical frame
        transformedRect = adjustRectForDisplay(rect: transformedRect, displayInfo: displayInfo)

        logger.debug(
            "After enhanced coordinate transformations: \(transformedRect)", service: serviceName)
        return transformedRect
    }

    /// Adjust rect to fit within display bounds using Workspace patterns
    private func adjustRectForDisplay(rect: CGRect, displayInfo: DisplayInfo) -> CGRect {
        let logicalFrame = displayInfo.logicalFrame
        var adjustedRect = rect

        // Ensure width doesn't exceed display width
        if adjustedRect.width > logicalFrame.width {
            adjustedRect.size.width = logicalFrame.width
        }

        // Ensure height doesn't exceed display height
        if adjustedRect.height > logicalFrame.height {
            adjustedRect.size.height = logicalFrame.height
        }

        // Ensure rect stays within display bounds
        if adjustedRect.maxX > logicalFrame.maxX {
            adjustedRect.origin.x = logicalFrame.maxX - adjustedRect.width
        }
        if adjustedRect.maxY > logicalFrame.maxY {
            adjustedRect.origin.y = logicalFrame.maxY - adjustedRect.height
        }
        if adjustedRect.minX < logicalFrame.minX {
            adjustedRect.origin.x = logicalFrame.minX
        }
        if adjustedRect.minY < logicalFrame.minY {
            adjustedRect.origin.y = logicalFrame.minY
        }

        return adjustedRect
    }

    // MARK: - Fallback Calculation

    /// Fallback calculation method when enhanced display info is not available
    private func calculateFallbackRect(
        for direction: WindowDirection,
        window: WindowInfo,
        screen: NSScreen,
        visibleFrameOnly: Bool
    ) -> CGRect {
        logger.warning("Using fallback calculation method", service: serviceName)

        let screenFrame = visibleFrameOnly ? screen.visibleFrame : screen.frame
        let rectParams = RectCalculationParameters(
            window: window,
            visibleFrameOfScreen: screenFrame,
            action: direction,
            frameOfScreen: screen.frame
        )

        return standardPositionCalculation.calculateRect(rectParams).rect
    }

    // MARK: - Enhanced Display Arrangement Detection

    /// Determine if screens are arranged vertically using enhanced coordinate system
    private func isVerticalScreenArrangement(screens: [NSScreen]) -> Bool {
        guard let coordinateSystem = screenDetection.getCoordinateSystem() else {
            logger.warning(
                "No coordinate system available for arrangement detection", service: serviceName)
            return false
        }

        // Use the enhanced coordinate system's arrangement detection
        let arrangement = coordinateSystem.determineArrangement()
        let isVertical = arrangement == "Vertical" || arrangement == "Mixed"

        logger.debug(
            "Display arrangement detected: \(arrangement), isVertical: \(isVertical)",
            service: serviceName
        )

        return isVertical
    }

    /// Get enhanced display arrangement information
    func getDisplayArrangementInfo() -> (arrangement: String, virtualBounds: CGRect)? {
        guard let coordinateSystem = screenDetection.getCoordinateSystem() else {
            return nil
        }

        return (
            arrangement: coordinateSystem.determineArrangement(),
            virtualBounds: coordinateSystem.virtualBounds
        )
    }
}

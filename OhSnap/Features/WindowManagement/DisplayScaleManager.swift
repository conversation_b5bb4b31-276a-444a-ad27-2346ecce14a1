import AppKit
import Foundation

/// Enhanced display scale manager using proven Workspace patterns
/// Simplified to use the same coordinate system as the Workspace feature
class DisplayScaleManager {
    static let shared = DisplayScaleManager()
    private let logger = LoggingService.shared
    private let serviceName = "DisplayScaleManager"

    // Use enhanced coordinate system for all operations
    private let screenDetection = ScreenDetectionService()

    /// Convert a rect from one screen's coordinate system to another using enhanced coordinate system
    /// This method is simplified to use the proven Workspace patterns
    func convertRect(rect: CGRect, fromScreen: NSScreen, toScreen: NSScreen) -> CGRect {
        // If the screens are the same, no conversion needed
        if fromScreen == toScreen {
            return rect
        }

        logger.debug(
            "Converting rect using enhanced coordinate system: \(rect)",
            service: serviceName
        )

        // Get display information for both screens
        guard let fromDisplayInfo = screenDetection.getDisplayInfo(for: fromScreen),
            let toDisplayInfo = screenDetection.getDisplayInfo(for: toScreen)
        else {
            logger.warning(
                "Could not get display info for coordinate conversion, returning original rect",
                service: serviceName)
            return rect
        }

        // Use the enhanced coordinate system for conversion
        // This follows the same pattern as the Workspace feature

        // Step 1: Convert rect to normalized coordinates relative to source display
        let normalizedRect = WindowLayoutManager.normalizeFrame(
            rect,
            in: fromDisplayInfo
        )

        // Step 2: Convert normalized coordinates back to absolute coordinates on target display
        let convertedRect = WindowLayoutManager.denormalizeFrame(
            normalizedRect,
            in: toDisplayInfo
        )

        logger.debug(
            "Converted rect using Workspace patterns: \(rect) -> \(convertedRect)",
            service: serviceName
        )

        return convertedRect
    }

    // MARK: - Enhanced Display Arrangement Detection

    /// Determine if screens are arranged vertically using enhanced coordinate system
    private func isVerticalScreenArrangement(screens: [NSScreen]) -> Bool {
        guard let coordinateSystem = screenDetection.getCoordinateSystem() else {
            logger.warning(
                "No coordinate system available for arrangement detection", service: serviceName)
            return false
        }

        let arrangement = coordinateSystem.determineArrangement()
        return arrangement == "Vertical" || arrangement == "Mixed"
    }

    /// Adjust a rect for a specific screen using enhanced display information
    func adjustRectForScreen(rect: CGRect, screen: NSScreen) -> CGRect {
        // Get enhanced display information
        guard let displayInfo = screenDetection.getDisplayInfo(for: screen) else {
            logger.warning(
                "Could not get display info for rect adjustment, using fallback",
                service: serviceName)
            return adjustRectFallback(rect: rect, screen: screen)
        }

        logger.debug(
            "Adjusting rect using enhanced display info: \(rect)",
            service: serviceName
        )

        // Use the logical frame for adjustment (includes menu bar area)
        let logicalFrame = displayInfo.logicalFrame
        var adjustedRect = rect

        // Ensure width doesn't exceed display width
        if adjustedRect.width > logicalFrame.width {
            adjustedRect.size.width = logicalFrame.width
        }

        // Ensure height doesn't exceed display height
        if adjustedRect.height > logicalFrame.height {
            adjustedRect.size.height = logicalFrame.height
        }

        // Ensure rect stays within display bounds
        if adjustedRect.maxX > logicalFrame.maxX {
            adjustedRect.origin.x = logicalFrame.maxX - adjustedRect.width
        }
        if adjustedRect.maxY > logicalFrame.maxY {
            adjustedRect.origin.y = logicalFrame.maxY - adjustedRect.height
        }
        if adjustedRect.minX < logicalFrame.minX {
            adjustedRect.origin.x = logicalFrame.minX
        }
        if adjustedRect.minY < logicalFrame.minY {
            adjustedRect.origin.y = logicalFrame.minY
        }

        // Ensure pixel alignment
        let alignedRect = CGRect(
            x: floor(adjustedRect.origin.x),
            y: floor(adjustedRect.origin.y),
            width: floor(adjustedRect.width),
            height: floor(adjustedRect.height)
        )

        logger.debug(
            "Adjusted rect using enhanced coordinate system: \(rect) -> \(alignedRect)",
            service: serviceName
        )

        return alignedRect
    }

    /// Fallback rect adjustment when enhanced display info is not available
    private func adjustRectFallback(rect: CGRect, screen: NSScreen) -> CGRect {
        let totalFrame = screen.frame
        var adjustedRect = rect

        // Basic bounds checking
        if adjustedRect.width > totalFrame.width {
            adjustedRect.size.width = totalFrame.width
        }
        if adjustedRect.height > totalFrame.height {
            adjustedRect.size.height = totalFrame.height
        }
        if adjustedRect.maxX > totalFrame.maxX {
            adjustedRect.origin.x = totalFrame.maxX - adjustedRect.width
        }
        if adjustedRect.maxY > totalFrame.maxY {
            adjustedRect.origin.y = totalFrame.maxY - adjustedRect.height
        }
        if adjustedRect.minX < totalFrame.minX {
            adjustedRect.origin.x = totalFrame.minX
        }
        if adjustedRect.minY < totalFrame.minY {
            adjustedRect.origin.y = totalFrame.minY
        }

        return adjustedRect
    }

    /// Convert a point from one screen's coordinate system to another using enhanced coordinate system
    func convertPoint(point: CGPoint, fromScreen: NSScreen, toScreen: NSScreen) -> CGPoint {
        // If the screens are the same, no conversion needed
        if fromScreen == toScreen {
            return point
        }

        // Use the enhanced coordinate system for point conversion
        // Create a small rect around the point and convert it
        let pointRect = CGRect(x: point.x, y: point.y, width: 1, height: 1)
        let convertedRect = convertRect(rect: pointRect, fromScreen: fromScreen, toScreen: toScreen)

        return convertedRect.origin
    }
}

import AppKit  // For NSScreen
import ApplicationServices  // For AXError type
import CoreGraphics
import Foundation

// MARK: - Window Direction Types

enum WindowDirection: Equatable, Codable {
    // Standard positions
    case leftHalf
    case rightHalf
    case topHalf
    case bottomHalf
    case topLeft
    case topRight
    case bottomLeft
    case bottomRight
    case maximize
    case center

    // Thirds
    case leftThird
    case centerThird
    case rightThird
    case leftTwoThirds
    case centerTwoThirds
    case rightTwoThirds

    // Quarters
    case topLeftQuarter
    case topRightQuarter
    case bottomLeftQuarter
    case bottomRightQuarter

    // Size changes
    case larger
    case smaller
    case largerWidth
    case smallerWidth

    // Special positions
    case almostMaximize
    case specified

    // Custom position
    case custom(CGRect)

    // Add custom Equatable implementation for the custom case
    static func == (lhs: WindowDirection, rhs: WindowDirection) -> Bool {
        switch (lhs, rhs) {
        case (.custom(let rect1), .custom(let rect2)):
            return rect1 == rect2
        case (let dir1, let dir2):
            return String(describing: dir1) == String(describing: dir2)
        }
    }

    // Add human-readable description
    var description: String {
        switch self {
        // Standard positions
        case .leftHalf: return "Left Half"
        case .rightHalf: return "Right Half"
        case .topHalf: return "Top Half"
        case .bottomHalf: return "Bottom Half"
        case .topLeft: return "Top Left"
        case .topRight: return "Top Right"
        case .bottomLeft: return "Bottom Left"
        case .bottomRight: return "Bottom Right"
        case .maximize: return "Maximize"
        case .center: return "Center"

        // Thirds
        case .leftThird: return "Left Third"
        case .centerThird: return "Center Third"
        case .rightThird: return "Right Third"
        case .leftTwoThirds: return "Left Two Thirds"
        case .centerTwoThirds: return "Center Two Thirds"
        case .rightTwoThirds: return "Right Two Thirds"

        // Quarters
        case .topLeftQuarter: return "Top Left Quarter"
        case .topRightQuarter: return "Top Right Quarter"
        case .bottomLeftQuarter: return "Bottom Left Quarter"
        case .bottomRightQuarter: return "Bottom Right Quarter"

        // Size changes
        case .larger: return "Make Larger"
        case .smaller: return "Make Smaller"
        case .largerWidth: return "Make Wider"
        case .smallerWidth: return "Make Narrower"

        // Special positions
        case .almostMaximize: return "Almost Maximize"
        case .specified: return "Specified Size"

        // Custom
        case .custom: return "Custom Position"
        }
    }

    // MARK: - Codable Implementation

    private enum CodingKeys: String, CodingKey {
        case type
        case rect
    }

    private enum DirectionType: String, Codable {
        // Standard positions
        case leftHalf, rightHalf, topHalf, bottomHalf, topLeft, topRight, bottomLeft, bottomRight
        case maximize, center

        // Thirds
        case leftThird, centerThird, rightThird
        case leftTwoThirds, centerTwoThirds, rightTwoThirds

        // Quarters
        case topLeftQuarter, topRightQuarter, bottomLeftQuarter, bottomRightQuarter

        // Size changes
        case larger, smaller, largerWidth, smallerWidth

        // Special positions
        case almostMaximize, specified

        // Custom
        case custom
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        switch self {
        // Custom case with rect data
        case .custom(let rect):
            try container.encode(DirectionType.custom, forKey: .type)
            try container.encode(
                [rect.origin.x, rect.origin.y, rect.size.width, rect.size.height], forKey: .rect)

        // Standard positions
        case .leftHalf:
            try container.encode(DirectionType.leftHalf, forKey: .type)
        case .rightHalf:
            try container.encode(DirectionType.rightHalf, forKey: .type)
        case .topHalf:
            try container.encode(DirectionType.topHalf, forKey: .type)
        case .bottomHalf:
            try container.encode(DirectionType.bottomHalf, forKey: .type)
        case .topLeft:
            try container.encode(DirectionType.topLeft, forKey: .type)
        case .topRight:
            try container.encode(DirectionType.topRight, forKey: .type)
        case .bottomLeft:
            try container.encode(DirectionType.bottomLeft, forKey: .type)
        case .bottomRight:
            try container.encode(DirectionType.bottomRight, forKey: .type)
        case .maximize:
            try container.encode(DirectionType.maximize, forKey: .type)
        case .center:
            try container.encode(DirectionType.center, forKey: .type)

        // Thirds
        case .leftThird:
            try container.encode(DirectionType.leftThird, forKey: .type)
        case .centerThird:
            try container.encode(DirectionType.centerThird, forKey: .type)
        case .rightThird:
            try container.encode(DirectionType.rightThird, forKey: .type)
        case .leftTwoThirds:
            try container.encode(DirectionType.leftTwoThirds, forKey: .type)
        case .centerTwoThirds:
            try container.encode(DirectionType.centerTwoThirds, forKey: .type)
        case .rightTwoThirds:
            try container.encode(DirectionType.rightTwoThirds, forKey: .type)

        // Quarters
        case .topLeftQuarter:
            try container.encode(DirectionType.topLeftQuarter, forKey: .type)
        case .topRightQuarter:
            try container.encode(DirectionType.topRightQuarter, forKey: .type)
        case .bottomLeftQuarter:
            try container.encode(DirectionType.bottomLeftQuarter, forKey: .type)
        case .bottomRightQuarter:
            try container.encode(DirectionType.bottomRightQuarter, forKey: .type)

        // Size changes
        case .larger:
            try container.encode(DirectionType.larger, forKey: .type)
        case .smaller:
            try container.encode(DirectionType.smaller, forKey: .type)
        case .largerWidth:
            try container.encode(DirectionType.largerWidth, forKey: .type)
        case .smallerWidth:
            try container.encode(DirectionType.smallerWidth, forKey: .type)

        // Special positions
        case .almostMaximize:
            try container.encode(DirectionType.almostMaximize, forKey: .type)
        case .specified:
            try container.encode(DirectionType.specified, forKey: .type)
        }
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let type = try container.decode(DirectionType.self, forKey: .type)

        switch type {
        // Custom case with rect data
        case .custom:
            let rectArray = try container.decode([CGFloat].self, forKey: .rect)
            guard rectArray.count == 4 else {
                throw DecodingError.dataCorruptedError(
                    forKey: .rect,
                    in: container,
                    debugDescription: "Expected 4 values for rect")
            }
            let rect = CGRect(
                x: rectArray[0], y: rectArray[1], width: rectArray[2], height: rectArray[3])
            self = .custom(rect)

        // Standard positions
        case .leftHalf: self = .leftHalf
        case .rightHalf: self = .rightHalf
        case .topHalf: self = .topHalf
        case .bottomHalf: self = .bottomHalf
        case .topLeft: self = .topLeft
        case .topRight: self = .topRight
        case .bottomLeft: self = .bottomLeft
        case .bottomRight: self = .bottomRight
        case .maximize: self = .maximize
        case .center: self = .center

        // Thirds
        case .leftThird: self = .leftThird
        case .centerThird: self = .centerThird
        case .rightThird: self = .rightThird
        case .leftTwoThirds: self = .leftTwoThirds
        case .centerTwoThirds: self = .centerTwoThirds
        case .rightTwoThirds: self = .rightTwoThirds

        // Quarters
        case .topLeftQuarter: self = .topLeftQuarter
        case .topRightQuarter: self = .topRightQuarter
        case .bottomLeftQuarter: self = .bottomLeftQuarter
        case .bottomRightQuarter: self = .bottomRightQuarter

        // Size changes
        case .larger: self = .larger
        case .smaller: self = .smaller
        case .largerWidth: self = .largerWidth
        case .smallerWidth: self = .smallerWidth

        // Special positions
        case .almostMaximize: self = .almostMaximize
        case .specified: self = .specified
        }
    }
}

enum ThirdWindowPosition {
    case left
    case center
    case right
}

enum SnapPosition: Equatable, Codable {
    case leftHalf
    case rightHalf
    case topHalf
    case bottomHalf
    case fullscreen
    case leftThird
    case centerThird
    case rightThird
    case topLeftQuarter
    case topRightQuarter
    case bottomLeftQuarter
    case bottomRightQuarter
    case leftTwoThirds
    case centerTwoThirds
    case rightTwoThirds
    case custom(CGRect)

    // Add human-readable description
    var description: String {
        switch self {
        case .leftHalf: return "Left Half"
        case .rightHalf: return "Right Half"
        case .topHalf: return "Top Half"
        case .bottomHalf: return "Bottom Half"
        case .fullscreen: return "Fullscreen"
        case .leftThird: return "Left Third"
        case .centerThird: return "Center Third"
        case .rightThird: return "Right Third"
        case .topLeftQuarter: return "Top Left Quarter"
        case .topRightQuarter: return "Top Right Quarter"
        case .bottomLeftQuarter: return "Bottom Left Quarter"
        case .bottomRightQuarter: return "Bottom Right Quarter"
        case .leftTwoThirds: return "Left Two Thirds"
        case .centerTwoThirds: return "Center Two Thirds"
        case .rightTwoThirds: return "Right Two Thirds"
        case .custom: return "Custom Position"
        }
    }

    // MARK: - Codable Implementation

    private enum CodingKeys: String, CodingKey {
        case type
        case rect
    }

    private enum PositionType: String, Codable {
        case leftHalf, rightHalf, topHalf, bottomHalf, fullscreen
        case leftThird, centerThird, rightThird
        case topLeftQuarter, topRightQuarter, bottomLeftQuarter, bottomRightQuarter
        case leftTwoThirds, centerTwoThirds, rightTwoThirds, custom
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        switch self {
        case .custom(let rect):
            try container.encode(PositionType.custom, forKey: .type)
            try container.encode(
                [rect.origin.x, rect.origin.y, rect.size.width, rect.size.height], forKey: .rect)
        case .leftHalf:
            try container.encode(PositionType.leftHalf, forKey: .type)
        case .rightHalf:
            try container.encode(PositionType.rightHalf, forKey: .type)
        case .topHalf:
            try container.encode(PositionType.topHalf, forKey: .type)
        case .bottomHalf:
            try container.encode(PositionType.bottomHalf, forKey: .type)
        case .fullscreen:
            try container.encode(PositionType.fullscreen, forKey: .type)
        case .leftThird:
            try container.encode(PositionType.leftThird, forKey: .type)
        case .centerThird:
            try container.encode(PositionType.centerThird, forKey: .type)
        case .rightThird:
            try container.encode(PositionType.rightThird, forKey: .type)
        case .topLeftQuarter:
            try container.encode(PositionType.topLeftQuarter, forKey: .type)
        case .topRightQuarter:
            try container.encode(PositionType.topRightQuarter, forKey: .type)
        case .bottomLeftQuarter:
            try container.encode(PositionType.bottomLeftQuarter, forKey: .type)
        case .bottomRightQuarter:
            try container.encode(PositionType.bottomRightQuarter, forKey: .type)
        case .leftTwoThirds:
            try container.encode(PositionType.leftTwoThirds, forKey: .type)
        case .centerTwoThirds:
            try container.encode(PositionType.centerTwoThirds, forKey: .type)
        case .rightTwoThirds:
            try container.encode(PositionType.rightTwoThirds, forKey: .type)
        }
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let type = try container.decode(PositionType.self, forKey: .type)

        switch type {
        case .custom:
            let rectArray = try container.decode([CGFloat].self, forKey: .rect)
            guard rectArray.count == 4 else {
                throw DecodingError.dataCorruptedError(
                    forKey: .rect,
                    in: container,
                    debugDescription: "Expected 4 values for rect")
            }
            let rect = CGRect(
                x: rectArray[0], y: rectArray[1], width: rectArray[2], height: rectArray[3])
            self = .custom(rect)
        case .leftHalf: self = .leftHalf
        case .rightHalf: self = .rightHalf
        case .topHalf: self = .topHalf
        case .bottomHalf: self = .bottomHalf
        case .fullscreen: self = .fullscreen
        case .leftThird: self = .leftThird
        case .centerThird: self = .centerThird
        case .rightThird: self = .rightThird
        case .topLeftQuarter: self = .topLeftQuarter
        case .topRightQuarter: self = .topRightQuarter
        case .bottomLeftQuarter: self = .bottomLeftQuarter
        case .bottomRightQuarter: self = .bottomRightQuarter
        case .leftTwoThirds: self = .leftTwoThirds
        case .centerTwoThirds: self = .centerTwoThirds
        case .rightTwoThirds: self = .rightTwoThirds
        }
    }
}

// MARK: - Error Types

enum AccessibilityError: Error, CustomStringConvertible {
    case failedToGetAttribute(AXError)
    case failedToSetAttribute(AXError)
    case invalidValue
    case failedToCreateValue
    case permissionDenied
    case timeout

    var description: String {
        switch self {
        case .failedToGetAttribute(let error):
            return "Failed to get accessibility attribute: \(error)"
        case .failedToSetAttribute(let error):
            return "Failed to set accessibility attribute: \(error)"
        case .invalidValue:
            return "Invalid accessibility value"
        case .failedToCreateValue:
            return "Failed to create accessibility value"
        case .permissionDenied:
            return "Accessibility permission denied"
        case .timeout:
            return "Operation timed out"
        }
    }
}

enum WindowMoveError: Error, CustomStringConvertible {
    case failedToGetWindowInfo
    case failedToGetDisplayInfo
    case windowIsFullscreen
    case failedToSetPosition
    case verificationFailed
    case noTargetScreen
    case invalidWindow

    var description: String {
        switch self {
        case .failedToGetWindowInfo:
            return "Failed to get window information"
        case .failedToGetDisplayInfo:
            return "Failed to get enhanced display information"
        case .windowIsFullscreen:
            return "Window is in fullscreen mode"
        case .failedToSetPosition:
            return "Failed to set window position"
        case .verificationFailed:
            return "Failed to verify window position after move"
        case .noTargetScreen:
            return "No target screen found"
        case .invalidWindow:
            return "Invalid or inaccessible window"
        }
    }
}

// MARK: - Protocols

protocol WindowManageable {
    func moveWindow(_ window: AXUIElement, to direction: WindowDirection, on screen: NSScreen)
        async throws
    func getWindowInfo(_ window: AXUIElement) async throws -> WindowInfo
}

protocol ScreenDetectable {
    func getScreenContaining(_ frame: CGRect) -> NSScreen?
    func getAllScreens() -> [NSScreen]
}

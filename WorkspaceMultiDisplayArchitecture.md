# Workspace Multi-Display Architecture Documentation

## Overview

This document provides a comprehensive analysis of how the Workspace feature in OhSnap handles multi-display scenarios. The Workspace feature demonstrates a robust, proven approach to multi-display window management that works flawlessly across different display arrangements.

## File Structure and Key Components

### Core Files in `OhSnap/Features/Workspace/`

1. **`Workspace.swift`** - Core data models

   - `WindowInfo`: Stores normalized window coordinates with display mapping
   - `DisplayArrangementInfo`: Captures display arrangement metadata
   - `Workspace`: Main workspace data structure with display arrangement tracking

2. **`WorkspaceService.swift`** - Main service class (1,239 lines)

   - Window restoration logic with multi-display support
   - Display arrangement change detection and migration
   - Comprehensive logging and status management

3. **`WorkspacePreview.swift`** - Preview system (822 lines)

   - Real-time multi-display preview rendering
   - Enhanced coordinate system integration
   - Display tag positioning logic

4. **`WindowCaptureService.swift`** - Window capture and normalization (374 lines)

   - Captures current window positions using CGWindowList API
   - Normalizes coordinates relative to displays
   - Consistent UUID generation for display identification

5. **`DisplayArrangementMigration.swift`** - Migration system (858 lines)
   - Analyzes display arrangement changes
   - Provides migration strategies with confidence scoring
   - Handles complex display configuration changes

### Supporting Infrastructure

6. **`OhSnap/Utilities/WindowLayoutManager.swift`** - Core coordinate system (1,200 lines)
   - `DisplayInfo` structure for comprehensive display information
   - `CoordinateSystem` for universal coordinate handling
   - Normalization and denormalization algorithms
   - Display detection and sorting logic

### Key Data Structures

```swift
// Core window information with normalized coordinates
struct WindowInfo: Codable {
    let frame: CGRect              // Normalized coordinates (0-1)
    let monitorID: UUID?           // Consistent display identifier
    let appBundleIdentifier: String?
    let isFullscreen: Bool
    let zOrder: Int               // Window stacking order
}

// Display arrangement metadata for migration
struct DisplayArrangementInfo: Codable, Equatable {
    let displayIDs: [CGDirectDisplayID]
    let mainDisplayID: CGDirectDisplayID
    let displayFrames: [CGDirectDisplayID: CGRect]
    let createdAt: Date
}

// Enhanced display information
struct DisplayInfo {
    let id: CGDirectDisplayID
    let physicalFrame: CGRect
    let logicalFrame: CGRect
    let visibleFrame: CGRect
    let scaleFactor: CGFloat
    let isMain: Bool
    let name: String?
    var heightDifferenceFromMain: CGFloat
}
```

## Core Architecture

### 1. Enhanced Coordinate System (`CoordinateSystem`)

The Workspace feature uses a sophisticated coordinate system that handles all display arrangements robustly:

```swift
struct CoordinateSystem {
    let displays: [DisplayInfo]
    let virtualBounds: CGRect
}
```

**Key Features:**

- **Virtual Bounds**: Creates a union rect of all displays to establish a universal coordinate space
- **Logical Coordinates**: Uses logical frames consistently across all displays
- **Arrangement Detection**: Automatically determines if displays are arranged horizontally, vertically, or mixed
- **Percentage-based Logic**: Determines which display a window belongs to based on percentage overlap

### 2. Display Information Management (`DisplayInfo`)

Each display is represented by a comprehensive `DisplayInfo` structure:

```swift
struct DisplayInfo {
    let id: CGDirectDisplayID
    let physicalFrame: CGRect
    let logicalFrame: CGRect
    let visibleFrame: CGRect
    let scaleFactor: CGFloat
    let isMain: Bool
    let name: String?
    var heightDifferenceFromMain: CGFloat
}
```

**Key Capabilities:**

- **Multiple Frame Types**: Tracks physical, logical, and visible frames
- **Scale Factor Handling**: Properly handles Retina displays
- **Main Display Detection**: Identifies main display by coordinates (0,0)
- **Height Difference Calculation**: Tracks height differences relative to main display

### 3. Window Layout Manager (`WindowLayoutManager`)

The central component that handles all coordinate transformations and display operations:

**Core Functions:**

- `getAllDisplays()`: Retrieves and sorts all displays consistently
- `normalizeFrame()`: Converts absolute coordinates to normalized (0-1) coordinates
- `denormalizeFrame()`: Converts normalized coordinates back to absolute coordinates
- `findBestDisplay()`: Determines which display a window belongs to using percentage logic
- `captureCurrentDisplayArrangement()`: Creates snapshots of display arrangements

## Step-by-Step Workflows

### 1. Window Capture Workflow

**Process in `WindowCaptureService.getCurrentAppsAndWindowPositionsNormalized()`:**

1. **Force Display Refresh**: `let _ = NSScreen.screens` ensures latest display arrangement
2. **Get Raw Window Data**: Use `CGWindowListCopyWindowInfo()` to capture all windows
3. **Filter System Apps**: Remove dock, notification center, and other system windows
4. **Get Display Information**: Call `WindowLayoutManager.getAllDisplays()` for current displays
5. **For Each Window**:
   - Find best display using `WindowLayoutManager.findBestDisplay(for: windowFrame)`
   - Calculate percentage overlap with display
   - Create consistent UUID from display ID
   - Normalize coordinates using `WindowLayoutManager.normalizeFrameWithLog()`
   - Store normalized `WindowInfo` with display UUID

### 2. Workspace Restoration Workflow

**Process in `WorkspaceService.restoreWorkspaceInternal()`:**

**Phase 1: Display Arrangement Analysis**

1. Capture current arrangement: `WindowLayoutManager.captureCurrentDisplayArrangement()`
2. Compare with saved arrangement from workspace
3. If changed, analyze using `DisplayArrangementMigrationService.analyzeArrangementChange()`
4. Apply migration if confidence > 30%: `WindowMigrationEngine.migrateWorkspace()`

**Phase 2: App Launching**

1. Identify apps to launch from window bundle IDs
2. Launch apps asynchronously without activation
3. Wait for launch completion with timeout
4. Allow time for apps to create windows

**Phase 3: Window Positioning**

1. Sort windows by z-order (back to front)
2. For each window:
   - Find running app by bundle ID
   - Locate window element using Accessibility API
   - Denormalize coordinates: `WindowLayoutManager.denormalizeFrame()`
   - Apply position and size using AX APIs

**Phase 4: Z-Order Restoration**

1. Activate apps in correct order (back to front)
2. Use `NSRunningApplication.activate()` for proper stacking

### 3. Preview Rendering Workflow

**Process in `WorkspacePreview.setupDisplays()`:**

1. **Display Detection**: Get all displays using `WindowLayoutManager.getAllDisplays()`
2. **Create Coordinate System**: Initialize `CoordinateSystem(displays: displays)`
3. **Calculate Virtual Bounds**: Union of all display frames
4. **For Each Display**:
   - Convert to preview coordinates using `coordinateSystem.convertToPreview()`
   - Position display tags based on arrangement (vertical/horizontal)
5. **For Each Window**:
   - Find target display by UUID matching
   - Denormalize window frame to absolute coordinates
   - Convert to preview coordinates for rendering

### 1. Coordinate Normalization

**Process:**

1. **Capture Window Frame**: Get absolute window coordinates from Accessibility API
2. **Find Best Display**: Use percentage-based logic to determine which display contains most of the window
3. **Apply Height Offset**: Adjust for height differences between displays before normalization
4. **Normalize**: Convert to 0-1 coordinates relative to the display's total frame
5. **Store with Display ID**: Associate normalized frame with consistent display UUID

**Formula:**

```swift
// Calculate position relative to display
let dx = windowFrame.minX - display.frame.minX
let adjustedY = windowFrame.minY - display.frame.minY

// Apply height difference offset
let dy = applyHeightDifferenceOffset(y: adjustedY, windowHeight: windowFrame.height, display: display)

// Normalize to 0-1 coordinates
let normalizedX = dx / display.frame.width
let normalizedY = dy / display.frame.height
```

### 2. Display Arrangement Migration

The system includes sophisticated migration capabilities when display arrangements change:

**Migration Strategies:**

- **Direct Mapping**: 1:1 display mapping when displays remain the same
- **Proportional Mapping**: Scale windows to fit new arrangement proportionally
- **Intelligent Distribution**: Distribute windows across available displays
- **Main Display Fallback**: Move all windows to main display as last resort

**Migration Analysis:**

- Analyzes old vs new display arrangements
- Calculates confidence scores for migration strategies
- Handles display addition, removal, and repositioning
- Provides automatic migration with user notification

### 3. Preview System Integration

The preview system demonstrates perfect multi-display handling:

**Features:**

- **Real-time Display Detection**: Automatically refreshes when displays change
- **Accurate Positioning**: Shows windows in correct relative positions across displays
- **Display Tags**: Intelligently positions display labels based on arrangement
- **Coordinate Conversion**: Seamlessly converts between coordinate systems for preview

## Key Design Patterns

### 1. Consistent UUID Generation

```swift
private static func createConsistentUUID(from displayID: CGDirectDisplayID) -> UUID {
    let displayIDString = String(displayID)
    let hexString = String(format: "%08x", displayID)
    let uuidString = "\(hexString.prefix(8))-\(hexString.prefix(4))-4\(hexString.prefix(3))-a\(hexString.prefix(3))-\(displayIDString.padding(toLength: 12, withPad: "0", startingAt: 0))"
    return UUID(uuidString: uuidString) ?? UUID()
}
```

### 2. Percentage-based Display Detection

```swift
static func findBestDisplay(for windowFrame: CGRect) -> DisplayInfo? {
    var bestDisplay: DisplayInfo? = displays.first(where: { $0.isMain })
    var largestPercentage: CGFloat = 0.0

    for display in displays {
        if display.logicalFrame.contains(windowFrame) {
            return display // Perfect fit
        }

        let intersection = display.logicalFrame.intersection(windowFrame)
        let percentage = (intersection.width * intersection.height) / (windowFrame.width * windowFrame.height)

        if percentage > largestPercentage {
            largestPercentage = percentage
            bestDisplay = display
        }
    }

    return bestDisplay
}
```

### 3. Height Difference Compensation

```swift
static func applyHeightDifferenceOffset(y: CGFloat, windowHeight: CGFloat, display: DisplayInfo) -> CGFloat {
    let normalizedHeightDifference = display.heightDifferenceFromMain / display.frame.height
    return y - (normalizedHeightDifference * display.frame.height)
}
```

## Logging and Debugging

The Workspace feature includes comprehensive logging that provides detailed information about:

- Display detection and arrangement analysis
- Window normalization calculations
- Coordinate transformations
- Migration decisions and confidence scores
- Preview positioning calculations

**Example Log Structure:**

```
┌─────────────────────────────────────────────────────────────────────────
│ ENHANCED COORDINATE SYSTEM
├─────────────────────────────────────────────────────────────────────────
│ - Preview size: 500.0 x 220.0
│ - Virtual bounds: (0.0, -1080.0, 3440.0, 2160.0)
│ - Display arrangement: Vertical
│ - Total displays: 3
└─────────────────────────────────────────────────────────────────────────
```

## Integration Points

### 1. Window Capture Service

- Captures current window positions using CGWindowList API
- Filters system applications appropriately
- Normalizes coordinates using WindowLayoutManager

### 2. Workspace Service

- Manages workspace persistence with display arrangement metadata
- Handles workspace restoration with migration support
- Provides status updates during restoration process

### 3. Preview System

- Real-time display arrangement detection
- Accurate multi-display window positioning
- Responsive to display configuration changes

## Success Factors

1. **Unified Coordinate System**: Single, consistent approach across all components
2. **Percentage-based Logic**: Robust window-to-display assignment
3. **Height Difference Compensation**: Proper handling of displays with different heights
4. **Comprehensive Logging**: Detailed debugging information for troubleshooting
5. **Migration Support**: Automatic adaptation to display arrangement changes
6. **Real-time Updates**: Responsive to display configuration changes

## Technical Implementation Details

### 1. Display Detection and Sorting

The system uses Core Graphics APIs to detect displays and sorts them consistently:

```swift
static func getAllDisplays() -> [DisplayInfo] {
    // Get active displays using CGGetActiveDisplayList
    let maxDisplays: UInt32 = 32
    var displayIDs = [CGDirectDisplayID](repeating: 0, count: Int(maxDisplays))
    var displayCount: UInt32 = 0

    let result = CGGetActiveDisplayList(maxDisplays, &displayIDs, &displayCount)

    // Sort displays for consistent ordering (main display first, then by position)
    displays.sort { first, second in
        if first.isMain { return true }
        if second.isMain { return false }

        // Sort by position (left to right, top to bottom)
        if abs(first.effectiveOrigin.y - second.effectiveOrigin.y) < 10 {
            return first.effectiveOrigin.x < second.effectiveOrigin.x
        }
        return first.effectiveOrigin.y < second.effectiveOrigin.y
    }
}
```

### 2. Window-to-Display Assignment Algorithm

The percentage-based algorithm ensures accurate window assignment:

```swift
static func percentageOf(_ rect: CGRect, withinFrameOfScreen frameOfScreen: CGRect) -> CGFloat {
    let intersection = rect.intersection(frameOfScreen)
    var result: CGFloat = 0.0

    if !intersection.isNull {
        let areaOfIntersection = intersection.width * intersection.height
        let areaOfRect = rect.width * rect.height
        result = areaOfRect > 0 ? areaOfIntersection / areaOfRect : 0
    }

    return result
}
```

### 3. Coordinate System Conversion

The enhanced coordinate system provides universal conversion capabilities:

```swift
func convertToPreview(_ frame: CGRect, previewSize: CGSize, padding: CGFloat = 10, isDisplay: Bool = false) -> CGRect {
    // Calculate scale to fit virtual bounds in preview
    let availableSize = CGSize(
        width: previewSize.width - (padding * 2),
        height: previewSize.height - (padding * 2)
    )

    let scaleX = availableSize.width / virtualBounds.width
    let scaleY = availableSize.height / virtualBounds.height
    let scale = min(scaleX, scaleY) * 0.9  // Leave breathing room

    // Transform frame coordinates relative to virtual bounds
    let relativeX = frame.origin.x - virtualBounds.origin.x
    let relativeY = frame.origin.y - virtualBounds.origin.y

    // Apply scale and centering
    return CGRect(
        x: relativeX * scale + centerOffsetX,
        y: relativeY * scale + centerOffsetY,
        width: max(frame.width * scale, 8),
        height: max(frame.height * scale, 8)
    )
}
```

### 4. Display Arrangement Change Detection

The system monitors for display changes and triggers appropriate responses:

```swift
.onReceive(NotificationCenter.default.publisher(for: NSApplication.didChangeScreenParametersNotification)) { _ in
    logger.info("Screen parameters changed - refreshing display information", service: serviceName)
    setupDisplays()
}
```

## Error Handling and Edge Cases

### 1. Missing Display Handling

- Falls back to main display when target display is not found
- Logs detailed information about display assignment failures
- Maintains window information even when display mapping fails

### 2. Display Arrangement Changes

- Automatic migration with confidence scoring
- User notification of migration results
- Fallback strategies for complex changes

### 3. Coordinate System Edge Cases

- Handles negative coordinates in multi-display arrangements
- Manages displays with different heights and scales
- Accounts for menu bar and dock positioning

## Performance Considerations

### 1. Display Information Caching

- Refreshes display information only when needed
- Uses efficient Core Graphics APIs for display detection
- Minimizes coordinate calculations through caching

### 2. Logging Optimization

- Comprehensive logging for debugging without performance impact
- Structured logging for easy filtering and analysis
- Conditional logging based on service categories

### 3. Memory Management

- Efficient data structures for display and window information
- Proper cleanup of Core Graphics resources
- Minimal memory footprint for coordinate calculations

## Conclusion

The Workspace feature's multi-display handling represents a mature, well-tested approach that successfully manages complex display arrangements. Its combination of normalized coordinates, percentage-based display detection, and comprehensive migration support provides a robust foundation for multi-display window management.

The key to its success is the consistent use of a single coordinate system throughout all components, avoiding the mixing of different approaches that can lead to positioning errors in complex display arrangements.

## Recommendations for WindowManager Redesign

Based on this analysis, the WindowManager should adopt the following patterns from the Workspace feature:

1. **Use the Enhanced Coordinate System**: Implement the same `CoordinateSystem` and `DisplayInfo` structures
2. **Adopt Percentage-based Display Detection**: Replace current display detection with the proven percentage-based approach
3. **Implement Consistent UUID Generation**: Use the same UUID generation method for display identification
4. **Add Height Difference Compensation**: Include the height difference offset calculations
5. **Use Comprehensive Logging**: Implement the same structured logging approach for debugging
6. **Handle Display Changes**: Add notification listeners for display arrangement changes
7. **Avoid Mixed Approaches**: Use only the Workspace coordinate system, not a combination of systems
